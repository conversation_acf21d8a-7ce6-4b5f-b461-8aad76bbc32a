<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtUserGradeMapper">
    <select id="getMerchantGradeList" resultType="com.fuint.repository.model.MtUserGrade">
        select * from mt_user_grade t where t.MERCHANT_ID = #{merchantId} and t.STATUS != 'D'
    </select>
</mapper>
