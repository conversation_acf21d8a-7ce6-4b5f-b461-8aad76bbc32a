<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtGoodsMapper">
    <sql id="Base_Column_List">
        `ID`, `TYPE`, `MERCHANT_ID`, `STORE_ID`, `NAME`, `CATE_ID`, `GOODS_NO`, `PLATFORM`, `IS_SINGLE_SPEC`,
        `LOGO`, `PRICE`, `LINE_PRICE`, `COST_PRICE`, `STOCK`, `WEI<PERSON><PERSON>`, `COUPON_IDS`, `INIT_SALE`, `SALE_POINT`,
        `CAN_USE_POINT`, `IS_MEMBER_DISCOUNT`, `SORT`, `CREATE_TIME`, `UPDATE_TIME`, `O<PERSON>ERATOR`, `STATUS`
    </sql>
    <select id="getStoreGoodsList" resultType="com.fuint.repository.model.MtGoods">
        select <include refid="Base_Column_List" /> from mt_goods t where (t.STORE_ID = #{storeId} or (t.STORE_ID = 0 AND t.id IN( SELECT s.GOODS_ID FROM mt_store_goods s WHERE s.STORE_ID = #{storeId} AND s.STATUS = 'A')))
        <if test="cateId != null and cateId > 0">
            AND t.CATE_ID = #{cateId}
        </if>
        <if test="platform != null and (platform == 'H5' or platform == 'MP-WEIXIN')">
            AND t.platform IN(0, 1)
        </if>
        <if test="platform != null and platform == 'PC'">
            AND t.platform IN(0, 2)
        </if>
        and t.MERCHANT_ID = #{merchantId}
        and t.PRICE > 0
        and t.STATUS = 'A' order by t.sort asc
    </select>

    <select id="searchStoreGoodsList" resultType="com.fuint.repository.model.MtGoods">
        select <include refid="Base_Column_List" /> from mt_goods t where t.STATUS = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
        <if test="storeId != null and storeId > 0">
            and (t.STORE_ID = #{storeId} or (t.STORE_ID = 0 AND t.id IN( SELECT s.GOODS_ID FROM mt_store_goods s WHERE s.STORE_ID = #{storeId} AND s.STATUS = 'A' )))
        </if>
        <if test="platform != null and (platform == 'H5' or platform == 'MP-WEIXIN')">
            AND t.platform IN(0, 1)
        </if>
        <if test="platform != null and platform == 'PC'">
            AND t.platform IN(0, 2)
        </if>
        and t.PRICE > 0
        and (t.name like concat('%',#{keyword},'%') or t.goods_no = #{keyword} or t.ID = #{keyword} or t.description like concat('%',#{keyword},'%'))
        order by t.sort asc
    </select>

    <select id="getByGoodsNo" resultType="com.fuint.repository.model.MtGoods">
        select * from mt_goods t where t.GOODS_NO = #{goodsNo} and t.STATUS = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getByGoodsName" resultType="com.fuint.repository.model.MtGoods">
        select <include refid="Base_Column_List" /> from mt_goods t where t.NAME = #{goodsName} and t.STATUS != 'D'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <update id="updateInitSale">
        update mt_goods t set t.INIT_SALE = t.INIT_SALE + #{saleNum} where t.ID = #{goodsId}
    </update>

    <select id="selectGoodsList" resultType="com.fuint.repository.bean.GoodsBean">
        SELECT g.LOGO as logo,g.STORE_ID as storeId,g.ID AS goodsId,g.CATE_ID AS cateId,g.GOODS_NO AS goodsNo,g.NAME as name,g.PRICE as price, g.STOCK as stock,s.`SPEC_IDS` as specIds,s.`PRICE` as skuPrice, s.STOCK as skuStock FROM `mt_goods` g LEFT JOIN `mt_goods_sku` s ON g.id = s.`GOODS_ID`
        WHERE g.`status` = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND g.MERCHANT_ID = #{merchantId}
        </if>
        <if test="storeId != null and storeId > 0">
            AND (g.STORE_ID = #{storeId} OR (g.STORE_ID = 0 AND g.id IN( SELECT s.GOODS_ID FROM mt_store_goods s WHERE s.STORE_ID = #{storeId} AND s.STATUS = 'A')))
        </if>
        <if test="cateId != null and cateId > 0">
            AND g.CATE_ID = #{cateId}
        </if>
        <if test="keyword != null">
            AND (g.name LIKE concat('%',#{keyword},'%') OR g.GOODS_NO = #{keyword} OR s.SKU_NO = #{keyword})
        </if>
        ORDER BY g.`SORT` ASC
    </select>

    <select id="getGoodsSaleTopList" resultType="com.fuint.repository.bean.GoodsTopBean">
        SELECT g.id AS ID,g.name AS NAME,g.GOODS_NO,SUM(o.`NUM`) AS num, SUM(o.price) as amount FROM `mt_goods` g,`mt_order_goods` o WHERE g.`ID` = o.`GOODS_ID`
        <if test="merchantId != null and merchantId > 0">
            AND g.MERCHANT_ID = #{merchantId}
        </if>
        <if test="storeId != null and storeId > 0">
            AND g.STORE_ID = #{storeId}
        </if>
        <if test="startTime != null">
            AND o.CREATE_TIME &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND o.CREATE_TIME &lt;= #{endTime}
        </if>
        GROUP BY g.`ID` ORDER BY SUM(o.`NUM`) DESC LIMIT 10
    </select>

    <update id="removeMerchantGoods">
        update mt_goods t set t.STATUS = 'D' where t.MERCHANT_ID = #{merchantId}
    </update>
</mapper>
