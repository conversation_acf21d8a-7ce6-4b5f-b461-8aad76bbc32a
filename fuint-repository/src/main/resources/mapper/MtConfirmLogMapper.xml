<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtConfirmLogMapper">
    <select id="getConfirmNum" resultType="long">
       SELECT COUNT(0) FROM mt_confirm_log where USER_COUPON_ID = #{userCouponId} and status = 'A'
    </select>

    <select id="getConfirmLogCount" resultType="long">
        SELECT COUNT(0) FROM mt_confirm_log t
        WHERE t.CREATE_TIME &gt; #{beginTime} and t.CREATE_TIME &lt;= #{endTime} and t.status = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
        <if test="storeId != null and storeId > 0">
            AND t.STORE_ID = #{storeId}
        </if>
    </select>

    <select id="getOrderConfirmLogList" resultType="com.fuint.repository.model.MtConfirmLog">
        select * from mt_confirm_log o where o.ORDER_ID = #{orderId} and status = 'A'
    </select>
</mapper>
