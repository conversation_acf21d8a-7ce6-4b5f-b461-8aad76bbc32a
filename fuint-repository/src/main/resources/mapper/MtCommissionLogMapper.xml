<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtCommissionLogMapper">
    <update id="confirmCommissionLog">
        update mt_commission_log t set t.STATUS = 'B',t.OPERATOR = #{operator} where t.SETTLE_UUID = #{uuid}
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </update>

    <update id="cancelCommissionLog">
        update mt_commission_log t set t.STATUS = 'A',t.OPERATOR = #{operator} where t.SETTLE_UUID = #{uuid}
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </update>
</mapper>
