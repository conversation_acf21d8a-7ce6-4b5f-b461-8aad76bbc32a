<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtTableMapper">
    <select id="getActiveTableList" resultType="com.fuint.repository.model.MtTable">
        select * from mt_table t where t.MERCHANT_ID = #{merchantId} and t.STATUS = 'A'
        <if test="storeId != null and storeId != ''">
            AND t.STORE_ID = #{storeId}
        </if>
    </select>

    <select id="queryTableByTableCode" resultType="com.fuint.repository.model.MtTable">
        select * from mt_table t where t.STORE_ID = #{storeId} and t.CODE = #{code} and t.STATUS = 'A' limit 1
    </select>

</mapper>
