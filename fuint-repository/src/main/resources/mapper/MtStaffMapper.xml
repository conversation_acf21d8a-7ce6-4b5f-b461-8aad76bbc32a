<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtStaffMapper">
    <update id="updateStatus">
        update mt_staff p set p.AUDITED_STATUS = #{status}, p.UPDATE_TIME = #{updateTime} where p.ID = #{id}
    </update>

    <select id="queryStaffByMobile" resultType="com.fuint.repository.model.MtStaff">
        select * from mt_staff t where t.MOBILE = #{mobile} and t.AUDITED_STATUS != 'D'
    </select>

    <select id="queryStaffByUserId" resultType="com.fuint.repository.model.MtStaff">
        select * from mt_staff t where t.USER_ID = #{userId} and t.AUDITED_STATUS != 'D'
    </select>
</mapper>
